#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试改进的表格生成逻辑
验证表头与内容的100%对应关系
"""

import sys
import os
import json
from multi_shape_table_generator import DataGenerator

def test_improved_table_generation():
    """测试改进的表格生成逻辑"""
    print("=" * 60)
    print("测试改进的表格生成逻辑")
    print("=" * 60)
    
    # 创建数据生成器
    data_generator = DataGenerator()
    
    # 测试不同的表格配置
    test_configs = [
        {'rows': 3, 'cols': 3, 'description': '3x3表格'},
        {'rows': 4, 'cols': 4, 'description': '4x4表格'},
        {'rows': 2, 'cols': 5, 'description': '2x5表格'},
        {'rows': 5, 'cols': 2, 'description': '5x2表格'},
    ]
    
    for i, config in enumerate(test_configs):
        print(f"\n测试 {i+1}: {config['description']}")
        print("-" * 40)
        
        # 生成表格数据和调试信息
        table_data, debug_info = data_generator.generate_table_data_with_debug(
            config['rows'], 
            config['cols'], 
            f"test_debug_{i+1}.json"
        )
        
        # 显示生成的表格
        print(f"主题类别: {debug_info['category']}")
        print(f"表格尺寸: {len(table_data)}行 x {len(table_data[0])}列")
        print("\n生成的表格:")
        
        # 打印表格
        for row_idx, row in enumerate(table_data):
            if row_idx == 0:
                print("表头:", " | ".join(row))
                print("-" * (len(" | ".join(row)) + 10))
            else:
                print(f"数据{row_idx}:", " | ".join(row))
        
        # 分析语义匹配情况
        print("\n语义匹配分析:")
        total_columns = len(debug_info['columns'])
        perfect_matches = 0
        
        for col_idx, col_info in debug_info['columns'].items():
            header = col_info['header']
            expected_type = col_info['expected_type']
            actual_type = col_info['actual_type']
            semantic_match = col_info['semantic_match']
            
            match_status = "✓" if semantic_match['is_match'] else "✗"
            confidence = semantic_match['confidence']
            
            print(f"  列{col_idx} [{header}]: {match_status} (置信度: {confidence:.1f})")
            print(f"    期望类型: {expected_type}, 实际类型: {actual_type}")
            
            if semantic_match['is_match'] and confidence >= 0.8:
                perfect_matches += 1
            
            if semantic_match['issues']:
                print(f"    问题: {'; '.join(semantic_match['issues'])}")
        
        # 计算匹配率
        match_rate = (perfect_matches / total_columns) * 100 if total_columns > 0 else 0
        print(f"\n匹配率: {perfect_matches}/{total_columns} ({match_rate:.1f}%)")
        
        # 显示列映射信息
        print("\n列映射信息:")
        for mapping in debug_info['column_mapping']:
            generator_status = "✓" if mapping['has_generator'] else "✗"
            print(f"  {mapping['header']}: 生成器 {generator_status}")

def test_all_categories():
    """测试所有数据类别"""
    print("\n" + "=" * 60)
    print("测试所有数据类别的表头与内容对应")
    print("=" * 60)
    
    data_generator = DataGenerator()
    categories = list(data_generator.data_categories.keys())
    
    for category in categories:
        print(f"\n测试类别: {category}")
        print("-" * 30)
        
        # 临时修改随机选择，强制使用特定类别
        original_choice = data_generator.data_categories
        
        # 创建只包含当前类别的字典
        temp_categories = {category: data_generator.data_categories[category]}
        data_generator.data_categories = temp_categories
        
        try:
            # 生成3x4表格
            table_data, debug_info = data_generator.generate_table_data_with_debug(3, 4)
            
            print(f"表头: {debug_info['headers']}")
            print("数据示例:")
            if len(table_data) > 1:
                print(f"  {table_data[1]}")
            
            # 检查匹配情况
            matches = sum(1 for col_info in debug_info['columns'].values() 
                         if col_info['semantic_match']['is_match'])
            total = len(debug_info['columns'])
            print(f"匹配率: {matches}/{total} ({(matches/total)*100:.1f}%)")
            
        finally:
            # 恢复原始类别字典
            data_generator.data_categories = original_choice

def main():
    """主函数"""
    try:
        # 测试改进的表格生成
        test_improved_table_generation()
        
        # 测试所有类别
        test_all_categories()
        
        print("\n" + "=" * 60)
        print("测试完成！")
        print("=" * 60)
        print("\n检查要点:")
        print("1. 表头与内容是否语义匹配")
        print("2. 匹配率是否达到90%以上")
        print("3. 每列是否都有专用的数据生成器")
        print("4. 调试信息是否正确保存")
        
    except Exception as e:
        print(f"测试过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
