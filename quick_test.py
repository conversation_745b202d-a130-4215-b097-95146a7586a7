#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
快速测试修复效果
"""

from semantic_analyzer import SemanticAnalyzer

def main():
    print("快速测试语义修复效果")
    print("=" * 40)
    
    analyzer = SemanticAnalyzer()
    results = analyzer.analyze_table_semantics(num_tables=10)
    
    print(f"匹配率: {results['match_rate']:.2f}%")
    print(f"问题数量: {results['issues_count']}")
    
    if results['match_rate'] >= 99:
        print("🎉 修复成功！达到99%以上匹配率")
    elif results['match_rate'] >= 95:
        print("✅ 修复良好！达到95%以上匹配率")
    elif results['match_rate'] >= 90:
        print("⚠️ 修复一般，达到90%以上匹配率")
    else:
        print("❌ 仍需进一步修复")

if __name__ == "__main__":
    main()
