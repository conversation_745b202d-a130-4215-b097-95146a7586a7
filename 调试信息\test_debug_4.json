{"category": "general", "headers": ["序号", "数值"], "column_mapping": [{"header": "序号", "has_generator": true}, {"header": "数值", "has_generator": true}], "columns": {"0": {"header": "序号", "data": ["1", "2", "3", "4"], "expected_type": "number", "actual_type": "quantity", "semantic_match": {"is_match": true, "confidence": 0.8, "issues": []}, "generator_used": true}, "1": {"header": "数值", "data": ["54", "27", "99", "9"], "expected_type": "general", "actual_type": "quantity", "semantic_match": {"is_match": false, "confidence": 0.0, "issues": ["期望类型: general, 实际类型: quantity", "表头: '数值', 示例数据: ['54', '27', '99']"]}, "generator_used": true}}}