{"category": "education", "headers": ["序号", "等级", "分数", "学科"], "column_mapping": [{"header": "序号", "has_generator": true}, {"header": "等级", "has_generator": true}, {"header": "分数", "has_generator": true}, {"header": "学科", "has_generator": true}], "columns": {"0": {"header": "序号", "data": ["1", "2", "3"], "expected_type": "number", "actual_type": "quantity", "semantic_match": {"is_match": true, "confidence": 0.8, "issues": []}, "generator_used": true}, "1": {"header": "等级", "data": ["A级", "中等", "良好"], "expected_type": "general", "actual_type": "general", "semantic_match": {"is_match": true, "confidence": 1.0, "issues": []}, "generator_used": true}, "2": {"header": "分数", "data": ["73", "68", "99"], "expected_type": "score", "actual_type": "quantity", "semantic_match": {"is_match": true, "confidence": 0.9, "issues": []}, "generator_used": true}, "3": {"header": "学科", "data": ["地理", "政治", "数学"], "expected_type": "general", "actual_type": "general", "semantic_match": {"is_match": true, "confidence": 1.0, "issues": []}, "generator_used": true}}}