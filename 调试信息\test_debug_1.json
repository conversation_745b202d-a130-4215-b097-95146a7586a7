{"category": "technology", "headers": ["序号", "状态", "更新时间"], "column_mapping": [{"header": "序号", "has_generator": true}, {"header": "状态", "has_generator": true}, {"header": "更新时间", "has_generator": true}], "columns": {"0": {"header": "序号", "data": ["1", "2"], "expected_type": "number", "actual_type": "quantity", "semantic_match": {"is_match": true, "confidence": 0.8, "issues": []}, "generator_used": true}, "1": {"header": "状态", "data": ["已发布", "开发中"], "expected_type": "status", "actual_type": "general", "semantic_match": {"is_match": true, "confidence": 0.8, "issues": []}, "generator_used": true}, "2": {"header": "更新时间", "data": ["2025-03-16", "2025-03-05"], "expected_type": "date", "actual_type": "date", "semantic_match": {"is_match": true, "confidence": 1.0, "issues": []}, "generator_used": true}}}