{"category": "location", "headers": ["序号", "人口", "面积", "城市", "GDP"], "column_mapping": [{"header": "序号", "has_generator": true}, {"header": "人口", "has_generator": true}, {"header": "面积", "has_generator": true}, {"header": "城市", "has_generator": true}, {"header": "GDP", "has_generator": true}], "columns": {"0": {"header": "序号", "data": ["1"], "expected_type": "number", "actual_type": "quantity", "semantic_match": {"is_match": true, "confidence": 0.8, "issues": []}, "generator_used": true}, "1": {"header": "人口", "data": ["1072万人"], "expected_type": "count_with_unit", "actual_type": "count_with_unit", "semantic_match": {"is_match": true, "confidence": 1.0, "issues": []}, "generator_used": true}, "2": {"header": "面积", "data": ["3700平方公里"], "expected_type": "count_with_unit", "actual_type": "count_with_unit", "semantic_match": {"is_match": true, "confidence": 1.0, "issues": []}, "generator_used": true}, "3": {"header": "城市", "data": ["武汉"], "expected_type": "location_name", "actual_type": "location_name", "semantic_match": {"is_match": true, "confidence": 1.0, "issues": []}, "generator_used": true}, "4": {"header": "GDP", "data": ["7071亿元"], "expected_type": "count_with_unit", "actual_type": "count_with_unit", "semantic_match": {"is_match": true, "confidence": 1.0, "issues": []}, "generator_used": true}}}